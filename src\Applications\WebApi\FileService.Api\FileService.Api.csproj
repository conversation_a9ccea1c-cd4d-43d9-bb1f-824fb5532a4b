<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.7"/>
        <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\Domains\FileService.Core\FileService.Core.csproj" />
        <ProjectReference Include="..\..\Infrastructures\FileService.HttpClients\FileService.HttpClients.csproj" />
    </ItemGroup>

</Project>
