using FileService.Core.Models;

namespace FileService.Core.Interfaces;

/// <summary>
/// Interface for Azure Blob Storage operations including SAS token generation
/// </summary>
public interface IAzureBlobService
{
    /// <summary>
    /// Generates a SAS token for the specified blob or container
    /// </summary>
    /// <param name="request">The SAS token request parameters</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The generated SAS token response</returns>
    Task<SasTokenResponse> GenerateSasTokenAsync(SasTokenRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Uploads a blob to Azure Storage
    /// </summary>
    /// <param name="request">The blob upload request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Information about the uploaded blob</returns>
    Task<BlobInfo> UploadBlobAsync(BlobUploadRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Downloads a blob from Azure Storage
    /// </summary>
    /// <param name="containerName">The container name</param>
    /// <param name="blobName">The blob name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The blob content as a stream</returns>
    Task<Stream> DownloadBlobAsync(string containerName, string blobName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets information about a specific blob
    /// </summary>
    /// <param name="containerName">The container name</param>
    /// <param name="blobName">The blob name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Information about the blob</returns>
    Task<BlobInfo?> GetBlobInfoAsync(string containerName, string blobName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Lists all blobs in a container
    /// </summary>
    /// <param name="containerName">The container name</param>
    /// <param name="prefix">Optional prefix to filter blobs</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>A list of blob information</returns>
    Task<IEnumerable<BlobInfo>> ListBlobsAsync(string containerName, string? prefix = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Deletes a blob from Azure Storage
    /// </summary>
    /// <param name="containerName">The container name</param>
    /// <param name="blobName">The blob name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if the blob was deleted, false if it didn't exist</returns>
    Task<bool> DeleteBlobAsync(string containerName, string blobName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a blob exists in Azure Storage
    /// </summary>
    /// <param name="containerName">The container name</param>
    /// <param name="blobName">The blob name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if the blob exists, false otherwise</returns>
    Task<bool> BlobExistsAsync(string containerName, string blobName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Creates a container if it doesn't exist
    /// </summary>
    /// <param name="containerName">The container name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if the container was created, false if it already existed</returns>
    Task<bool> CreateContainerIfNotExistsAsync(string containerName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Deletes a container and all its blobs
    /// </summary>
    /// <param name="containerName">The container name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if the container was deleted, false if it didn't exist</returns>
    Task<bool> DeleteContainerAsync(string containerName, CancellationToken cancellationToken = default);
}
