using FileService.Core.Interfaces;
using FileService.Core.Models;
using Microsoft.AspNetCore.Mvc;

namespace FileService.Api.Controllers;

/// <summary>
/// Controller for generating SAS tokens for Azure Blob Storage
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class SasTokenController : ControllerBase
{
    private readonly IAzureBlobSasService _sasService;

    public SasTokenController(IAzureBlobSasService sasService)
    {
        _sasService = sasService;
    }

    /// <summary>
    /// Generates a read-only SAS token for a specific blob
    /// </summary>
    /// <param name="containerName">The container name</param>
    /// <param name="blobName">The blob name</param>
    /// <param name="expiryHours">Hours until the token expires (default: 1)</param>
    /// <returns>SAS token response</returns>
    [HttpGet("blob")]
    public async Task<ActionResult<SasTokenResponse>> GenerateBlobSasToken(
        [FromQuery] string containerName,
        [FromQuery] string blobName,
        [FromQuery] int expiryHours = 1)
    {
        try
        {
            var request = new SasTokenRequest
            {
                ContainerName = containerName,
                BlobName = blobName,
                ExpiresOn = DateTimeOffset.UtcNow.AddHours(expiryHours)
            };

            var response = await _sasService.GenerateSasTokenAsync(request);
            return Ok(response);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error generating SAS token: {ex.Message}");
        }
    }

    /// <summary>
    /// Generates a read-only SAS token for a container
    /// </summary>
    /// <param name="containerName">The container name</param>
    /// <param name="expiryHours">Hours until the token expires (default: 1)</param>
    /// <returns>SAS token response</returns>
    [HttpGet("container")]
    public async Task<ActionResult<SasTokenResponse>> GenerateContainerSasToken(
        [FromQuery] string containerName,
        [FromQuery] int expiryHours = 1)
    {
        try
        {
            var request = new SasTokenRequest
            {
                ContainerName = containerName,
                ExpiresOn = DateTimeOffset.UtcNow.AddHours(expiryHours)
            };

            var response = await _sasService.GenerateSasTokenAsync(request);
            return Ok(response);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error generating SAS token: {ex.Message}");
        }
    }
}
