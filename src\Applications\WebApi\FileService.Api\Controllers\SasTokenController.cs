using FileService.Core.Configuration;
using FileService.Core.Interfaces;
using FileService.Core.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;

namespace FileService.Api.Controllers;

/// <summary>
/// Controller for generating SAS tokens for Azure Blob Storage
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class SasTokenController : ControllerBase
{
    private readonly IAzureBlobSasService _sasService;
    private readonly AzureStorageOptions _storageOptions;

    public SasTokenController(IAzureBlobSasService sasService, IOptions<AzureStorageOptions> storageOptions)
    {
        _sasService = sasService;
        _storageOptions = storageOptions.Value;
    }

    /// <summary>
    /// Generates a read-only SAS token for a specific blob
    /// </summary>
    /// <param name="projectName">The project name (must match configuration, e.g., "rehash", "reconnect")</param>
    /// <param name="containerName">The container name</param>
    /// <param name="blobName">The blob name</param>
    /// <param name="expiryHours">Hours until the token expires (default: 1)</param>
    /// <returns>SAS token response</returns>
    [HttpGet("blob")]
    public async Task<ActionResult<SasTokenResponse>> GenerateBlobSasToken(
        [FromQuery] string projectName,
        [FromQuery] string containerName,
        [FromQuery] string blobName,
        [FromQuery] int expiryHours = 1)
    {
        try
        {
            var request = new SasTokenRequest
            {
                ProjectName = projectName,
                ContainerName = containerName,
                BlobName = blobName,
                ExpiresOn = DateTimeOffset.UtcNow.AddHours(expiryHours)
            };

            var response = await _sasService.GenerateSasTokenAsync(request);
            return Ok(response);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error generating SAS token: {ex.Message}");
        }
    }

    /// <summary>
    /// Generates a read-only SAS token for a container
    /// </summary>
    /// <param name="storageAccountName">The storage account name (must match configuration)</param>
    /// <param name="containerName">The container name</param>
    /// <param name="expiryHours">Hours until the token expires (default: 1)</param>
    /// <returns>SAS token response</returns>
    [HttpGet("container")]
    public async Task<ActionResult<SasTokenResponse>> GenerateContainerSasToken(
        [FromQuery] string storageAccountName,
        [FromQuery] string containerName,
        [FromQuery] int expiryHours = 1)
    {
        try
        {
            var request = new SasTokenRequest
            {
                StorageAccountName = storageAccountName,
                ContainerName = containerName,
                ExpiresOn = DateTimeOffset.UtcNow.AddHours(expiryHours)
            };

            var response = await _sasService.GenerateSasTokenAsync(request);
            return Ok(response);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error generating SAS token: {ex.Message}");
        }
    }

    /// <summary>
    /// Gets information about available storage accounts and their containers
    /// </summary>
    /// <returns>List of storage accounts with their containers</returns>
    [HttpGet("accounts")]
    public ActionResult<object> GetStorageAccounts()
    {
        try
        {
            var accounts = _storageOptions.StorageAccounts.Select(kvp => new
            {
                StorageAccountName = kvp.Key,
                Containers = kvp.Value.Containers,
                ContainerCount = kvp.Value.Containers.Count
            }).ToList();

            return Ok(new
            {
                TotalAccounts = accounts.Count,
                StorageAccounts = accounts
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error retrieving storage accounts: {ex.Message}");
        }
    }
}
