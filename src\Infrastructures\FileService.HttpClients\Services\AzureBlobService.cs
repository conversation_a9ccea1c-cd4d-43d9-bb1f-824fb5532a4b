using Azure.Storage.Blobs;
using Azure.Storage.Sas;
using FileService.Core.Configuration;
using FileService.Core.Interfaces;
using FileService.Core.Models;
using Microsoft.Extensions.Options;

namespace FileService.HttpClients.Services;

/// <summary>
/// Implementation of Azure Blob Storage SAS token generation service
/// </summary>
public class AzureBlobSasService : IAzureBlobSasService
{
    private readonly BlobServiceClient _blobServiceClient;
    private readonly AzureStorageOptions _options;

    public AzureBlobSasService(IOptions<AzureStorageOptions> options)
    {
        _options = options.Value ?? throw new ArgumentNullException(nameof(options));
        
        if (!_options.IsValid())
            throw new InvalidOperationException("Invalid Azure Storage configuration");

        _blobServiceClient = CreateBlobServiceClient();
    }

    /// <summary>
    /// Generates a SAS token for the specified blob or container
    /// </summary>
    public async Task<SasTokenResponse> GenerateSasTokenAsync(SasTokenRequest request, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(request);
        
        if (string.IsNullOrEmpty(request.ContainerName))
            throw new ArgumentException("Container name is required", nameof(request));

        // Validate expiry time
        var maxExpiry = DateTimeOffset.UtcNow.AddHours(_options.MaxSasExpiryHours);
        if (request.ExpiresOn > maxExpiry)
            throw new ArgumentException($"SAS token expiry cannot exceed {_options.MaxSasExpiryHours} hours", nameof(request));

        var containerClient = _blobServiceClient.GetBlobContainerClient(request.ContainerName);

        // Ensure container exists
        await containerClient.CreateIfNotExistsAsync(cancellationToken: cancellationToken);

        if (!string.IsNullOrEmpty(request.BlobName))
        {
            // Generate blob-level SAS
            return await GenerateBlobSasTokenAsync(containerClient, request, cancellationToken);
        }
        else
        {
            // Generate container-level SAS
            return GenerateContainerSasToken(containerClient, request);
        }
    }



    private BlobServiceClient CreateBlobServiceClient()
    {
        if (!string.IsNullOrEmpty(_options.ConnectionString))
        {
            return new BlobServiceClient(_options.ConnectionString);
        }

        if (!string.IsNullOrEmpty(_options.AccountName))
        {
            var serviceUri = _options.GetBlobServiceUri();
            if (serviceUri == null)
                throw new InvalidOperationException("Unable to determine blob service URI");

            if (!string.IsNullOrEmpty(_options.AccountKey))
            {
                var credential = new Azure.Storage.StorageSharedKeyCredential(_options.AccountName, _options.AccountKey);
                return new BlobServiceClient(serviceUri, credential);
            }

            if (!string.IsNullOrEmpty(_options.SasToken))
            {
                var sasUri = new UriBuilder(serviceUri) { Query = _options.SasToken }.Uri;
                return new BlobServiceClient(sasUri);
            }
        }

        throw new InvalidOperationException("Invalid Azure Storage configuration");
    }

    private async Task<SasTokenResponse> GenerateBlobSasTokenAsync(BlobContainerClient containerClient, SasTokenRequest request, CancellationToken cancellationToken)
    {
        var blobClient = containerClient.GetBlobClient(request.BlobName!);

        if (!blobClient.CanGenerateSasUri)
            throw new InvalidOperationException("Cannot generate SAS token with current authentication method");

        var sasBuilder = new BlobSasBuilder
        {
            BlobContainerName = request.ContainerName,
            BlobName = request.BlobName,
            Resource = "b", // blob
            ExpiresOn = request.ExpiresOn
        };

        if (request.StartsOn.HasValue)
            sasBuilder.StartsOn = request.StartsOn.Value;

        // Map permissions
        sasBuilder.SetPermissions(MapToAzureSasPermissions(request.Permissions));

        // Set content headers if specified
        if (!string.IsNullOrEmpty(request.ContentType))
            sasBuilder.ContentType = request.ContentType;

        if (!string.IsNullOrEmpty(request.ContentDisposition))
            sasBuilder.ContentDisposition = request.ContentDisposition;

        var sasToken = blobClient.GenerateSasUri(sasBuilder).Query;
        var sasUri = blobClient.Uri + sasToken;

        return new SasTokenResponse
        {
            SasToken = sasToken,
            SasUri = sasUri,
            ExpiresOn = request.ExpiresOn,
            StartsOn = request.StartsOn,
            Permissions = request.Permissions,
            ContainerName = request.ContainerName,
            BlobName = request.BlobName
        };
    }

    private SasTokenResponse GenerateContainerSasToken(BlobContainerClient containerClient, SasTokenRequest request)
    {
        if (!containerClient.CanGenerateSasUri)
            throw new InvalidOperationException("Cannot generate SAS token with current authentication method");

        var sasBuilder = new BlobSasBuilder
        {
            BlobContainerName = request.ContainerName,
            Resource = "c", // container
            ExpiresOn = request.ExpiresOn
        };

        if (request.StartsOn.HasValue)
            sasBuilder.StartsOn = request.StartsOn.Value;

        // Map permissions
        sasBuilder.SetPermissions(MapToAzureSasPermissions(request.Permissions));

        var sasToken = containerClient.GenerateSasUri(sasBuilder).Query;
        var sasUri = containerClient.Uri + sasToken;

        return new SasTokenResponse
        {
            SasToken = sasToken,
            SasUri = sasUri,
            ExpiresOn = request.ExpiresOn,
            StartsOn = request.StartsOn,
            Permissions = request.Permissions,
            ContainerName = request.ContainerName
        };
    }

    private static BlobSasPermissions MapToAzureSasPermissions(SasPermissions permissions)
    {
        var azurePermissions = BlobSasPermissions.None;

        if (permissions.HasFlag(SasPermissions.Read))
            azurePermissions |= BlobSasPermissions.Read;

        if (permissions.HasFlag(SasPermissions.Write))
            azurePermissions |= BlobSasPermissions.Write;

        if (permissions.HasFlag(SasPermissions.Delete))
            azurePermissions |= BlobSasPermissions.Delete;

        if (permissions.HasFlag(SasPermissions.List))
            azurePermissions |= BlobSasPermissions.List;

        if (permissions.HasFlag(SasPermissions.Add))
            azurePermissions |= BlobSasPermissions.Add;

        if (permissions.HasFlag(SasPermissions.Create))
            azurePermissions |= BlobSasPermissions.Create;

        if (permissions.HasFlag(SasPermissions.Update))
            azurePermissions |= BlobSasPermissions.Write; // Update maps to Write in Azure

        if (permissions.HasFlag(SasPermissions.Process))
            azurePermissions |= BlobSasPermissions.Read; // Process typically requires read

        if (permissions.HasFlag(SasPermissions.Tag))
            azurePermissions |= BlobSasPermissions.Tag;

        if (permissions.HasFlag(SasPermissions.Filter))
            azurePermissions |= BlobSasPermissions.Filter;

        return azurePermissions;
    }


}
