using Azure;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Azure.Storage.Sas;
using FileService.Core.Configuration;
using FileService.Core.Interfaces;
using FileService.Core.Models;
using Microsoft.Extensions.Options;

namespace FileService.HttpClients.Services;

/// <summary>
/// Implementation of Azure Blob Storage service with SAS token generation
/// </summary>
public class AzureBlobService : IAzureBlobService
{
    private readonly BlobServiceClient _blobServiceClient;
    private readonly AzureStorageOptions _options;

    public AzureBlobService(IOptions<AzureStorageOptions> options)
    {
        _options = options.Value ?? throw new ArgumentNullException(nameof(options));
        
        if (!_options.IsValid())
            throw new InvalidOperationException("Invalid Azure Storage configuration");

        _blobServiceClient = CreateBlobServiceClient();
    }

    /// <summary>
    /// Generates a SAS token for the specified blob or container
    /// </summary>
    public async Task<SasTokenResponse> GenerateSasTokenAsync(SasTokenRequest request, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(request);
        
        if (string.IsNullOrEmpty(request.ContainerName))
            throw new ArgumentException("Container name is required", nameof(request));

        // Validate expiry time
        var maxExpiry = DateTimeOffset.UtcNow.AddHours(_options.MaxSasExpiryHours);
        if (request.ExpiresOn > maxExpiry)
            throw new ArgumentException($"SAS token expiry cannot exceed {_options.MaxSasExpiryHours} hours", nameof(request));

        var containerClient = _blobServiceClient.GetBlobContainerClient(request.ContainerName);

        // Ensure container exists
        await containerClient.CreateIfNotExistsAsync(cancellationToken: cancellationToken);

        if (!string.IsNullOrEmpty(request.BlobName))
        {
            // Generate blob-level SAS
            return await GenerateBlobSasTokenAsync(containerClient, request, cancellationToken);
        }
        else
        {
            // Generate container-level SAS
            return GenerateContainerSasToken(containerClient, request);
        }
    }

    /// <summary>
    /// Uploads a blob to Azure Storage
    /// </summary>
    public async Task<BlobInfo> UploadBlobAsync(BlobUploadRequest request, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(request);
        
        if (string.IsNullOrEmpty(request.ContainerName))
            throw new ArgumentException("Container name is required", nameof(request));
        
        if (string.IsNullOrEmpty(request.BlobName))
            throw new ArgumentException("Blob name is required", nameof(request));

        var containerClient = _blobServiceClient.GetBlobContainerClient(request.ContainerName);
        await containerClient.CreateIfNotExistsAsync(cancellationToken: cancellationToken);

        var blobClient = containerClient.GetBlobClient(request.BlobName);

        var uploadOptions = new BlobUploadOptions
        {
            HttpHeaders = new BlobHttpHeaders
            {
                ContentType = request.ContentType
            },
            Metadata = request.Metadata,
            Conditions = request.Overwrite ? null : new BlobRequestConditions { IfNoneMatch = ETag.All }
        };

        if (!string.IsNullOrEmpty(request.AccessTier))
        {
            if (Enum.TryParse<AccessTier>(request.AccessTier, true, out var tier))
            {
                uploadOptions.AccessTier = tier;
            }
        }

        var response = await blobClient.UploadAsync(request.Content, uploadOptions, cancellationToken);
        var properties = await blobClient.GetPropertiesAsync(cancellationToken: cancellationToken);

        return MapToBlobInfo(blobClient, properties.Value);
    }

    /// <summary>
    /// Downloads a blob from Azure Storage
    /// </summary>
    public async Task<Stream> DownloadBlobAsync(string containerName, string blobName, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(containerName))
            throw new ArgumentException("Container name is required", nameof(containerName));
        
        if (string.IsNullOrEmpty(blobName))
            throw new ArgumentException("Blob name is required", nameof(blobName));

        var blobClient = _blobServiceClient.GetBlobContainerClient(containerName).GetBlobClient(blobName);
        
        if (!await blobClient.ExistsAsync(cancellationToken))
            throw new FileNotFoundException($"Blob '{blobName}' not found in container '{containerName}'");

        var response = await blobClient.DownloadStreamingAsync(cancellationToken: cancellationToken);
        return response.Value.Content;
    }

    /// <summary>
    /// Gets information about a specific blob
    /// </summary>
    public async Task<BlobInfo?> GetBlobInfoAsync(string containerName, string blobName, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(containerName))
            throw new ArgumentException("Container name is required", nameof(containerName));
        
        if (string.IsNullOrEmpty(blobName))
            throw new ArgumentException("Blob name is required", nameof(blobName));

        var blobClient = _blobServiceClient.GetBlobContainerClient(containerName).GetBlobClient(blobName);
        
        if (!await blobClient.ExistsAsync(cancellationToken))
            return null;

        var properties = await blobClient.GetPropertiesAsync(cancellationToken: cancellationToken);
        return MapToBlobInfo(blobClient, properties.Value);
    }

    /// <summary>
    /// Lists all blobs in a container
    /// </summary>
    public async Task<IEnumerable<BlobInfo>> ListBlobsAsync(string containerName, string? prefix = null, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(containerName))
            throw new ArgumentException("Container name is required", nameof(containerName));

        var containerClient = _blobServiceClient.GetBlobContainerClient(containerName);
        
        if (!await containerClient.ExistsAsync(cancellationToken))
            return Enumerable.Empty<BlobInfo>();

        var blobs = new List<BlobInfo>();
        
        await foreach (var blobItem in containerClient.GetBlobsAsync(prefix: prefix, cancellationToken: cancellationToken))
        {
            var blobClient = containerClient.GetBlobClient(blobItem.Name);
            blobs.Add(MapToBlobInfo(blobClient, blobItem));
        }

        return blobs;
    }

    /// <summary>
    /// Deletes a blob from Azure Storage
    /// </summary>
    public async Task<bool> DeleteBlobAsync(string containerName, string blobName, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(containerName))
            throw new ArgumentException("Container name is required", nameof(containerName));
        
        if (string.IsNullOrEmpty(blobName))
            throw new ArgumentException("Blob name is required", nameof(blobName));

        var blobClient = _blobServiceClient.GetBlobContainerClient(containerName).GetBlobClient(blobName);
        
        var response = await blobClient.DeleteIfExistsAsync(cancellationToken: cancellationToken);
        return response.Value;
    }

    /// <summary>
    /// Checks if a blob exists in Azure Storage
    /// </summary>
    public async Task<bool> BlobExistsAsync(string containerName, string blobName, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(containerName))
            throw new ArgumentException("Container name is required", nameof(containerName));
        
        if (string.IsNullOrEmpty(blobName))
            throw new ArgumentException("Blob name is required", nameof(blobName));

        var blobClient = _blobServiceClient.GetBlobContainerClient(containerName).GetBlobClient(blobName);
        var response = await blobClient.ExistsAsync(cancellationToken);
        return response.Value;
    }

    /// <summary>
    /// Creates a container if it doesn't exist
    /// </summary>
    public async Task<bool> CreateContainerIfNotExistsAsync(string containerName, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(containerName))
            throw new ArgumentException("Container name is required", nameof(containerName));

        var containerClient = _blobServiceClient.GetBlobContainerClient(containerName);
        var response = await containerClient.CreateIfNotExistsAsync(cancellationToken: cancellationToken);
        return response != null;
    }

    /// <summary>
    /// Deletes a container and all its blobs
    /// </summary>
    public async Task<bool> DeleteContainerAsync(string containerName, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(containerName))
            throw new ArgumentException("Container name is required", nameof(containerName));

        var containerClient = _blobServiceClient.GetBlobContainerClient(containerName);
        var response = await containerClient.DeleteIfExistsAsync(cancellationToken: cancellationToken);
        return response.Value;
    }

    private BlobServiceClient CreateBlobServiceClient()
    {
        if (!string.IsNullOrEmpty(_options.ConnectionString))
        {
            return new BlobServiceClient(_options.ConnectionString);
        }

        if (!string.IsNullOrEmpty(_options.AccountName))
        {
            var serviceUri = _options.GetBlobServiceUri();
            if (serviceUri == null)
                throw new InvalidOperationException("Unable to determine blob service URI");

            if (!string.IsNullOrEmpty(_options.AccountKey))
            {
                var credential = new Azure.Storage.StorageSharedKeyCredential(_options.AccountName, _options.AccountKey);
                return new BlobServiceClient(serviceUri, credential);
            }

            if (!string.IsNullOrEmpty(_options.SasToken))
            {
                var sasUri = new UriBuilder(serviceUri) { Query = _options.SasToken }.Uri;
                return new BlobServiceClient(sasUri);
            }
        }

        throw new InvalidOperationException("Invalid Azure Storage configuration");
    }

    private async Task<SasTokenResponse> GenerateBlobSasTokenAsync(BlobContainerClient containerClient, SasTokenRequest request, CancellationToken cancellationToken)
    {
        var blobClient = containerClient.GetBlobClient(request.BlobName!);

        // Ensure blob exists or can be created
        if (!await blobClient.ExistsAsync(cancellationToken))
        {
            // For write operations, we don't need the blob to exist
            if (!request.Permissions.HasFlag(SasPermissions.Write) &&
                !request.Permissions.HasFlag(SasPermissions.Create) &&
                !request.Permissions.HasFlag(SasPermissions.Add))
            {
                throw new FileNotFoundException($"Blob '{request.BlobName}' not found in container '{request.ContainerName}'");
            }
        }

        if (!blobClient.CanGenerateSasUri)
            throw new InvalidOperationException("Cannot generate SAS token with current authentication method");

        var sasBuilder = new BlobSasBuilder
        {
            BlobContainerName = request.ContainerName,
            BlobName = request.BlobName,
            Resource = "b", // blob
            ExpiresOn = request.ExpiresOn
        };

        if (request.StartsOn.HasValue)
            sasBuilder.StartsOn = request.StartsOn.Value;

        // Map permissions
        sasBuilder.SetPermissions(MapToAzureSasPermissions(request.Permissions));

        // Set content headers if specified
        if (!string.IsNullOrEmpty(request.ContentType))
            sasBuilder.ContentType = request.ContentType;

        if (!string.IsNullOrEmpty(request.ContentDisposition))
            sasBuilder.ContentDisposition = request.ContentDisposition;

        var sasToken = blobClient.GenerateSasUri(sasBuilder).Query;
        var sasUri = blobClient.Uri + sasToken;

        return new SasTokenResponse
        {
            SasToken = sasToken,
            SasUri = sasUri,
            ExpiresOn = request.ExpiresOn,
            StartsOn = request.StartsOn,
            Permissions = request.Permissions,
            ContainerName = request.ContainerName,
            BlobName = request.BlobName
        };
    }

    private SasTokenResponse GenerateContainerSasToken(BlobContainerClient containerClient, SasTokenRequest request)
    {
        if (!containerClient.CanGenerateSasUri)
            throw new InvalidOperationException("Cannot generate SAS token with current authentication method");

        var sasBuilder = new BlobSasBuilder
        {
            BlobContainerName = request.ContainerName,
            Resource = "c", // container
            ExpiresOn = request.ExpiresOn
        };

        if (request.StartsOn.HasValue)
            sasBuilder.StartsOn = request.StartsOn.Value;

        // Map permissions
        sasBuilder.SetPermissions(MapToAzureSasPermissions(request.Permissions));

        var sasToken = containerClient.GenerateSasUri(sasBuilder).Query;
        var sasUri = containerClient.Uri + sasToken;

        return new SasTokenResponse
        {
            SasToken = sasToken,
            SasUri = sasUri,
            ExpiresOn = request.ExpiresOn,
            StartsOn = request.StartsOn,
            Permissions = request.Permissions,
            ContainerName = request.ContainerName
        };
    }

    private static BlobSasPermissions MapToAzureSasPermissions(SasPermissions permissions)
    {
        var azurePermissions = BlobSasPermissions.None;

        if (permissions.HasFlag(SasPermissions.Read))
            azurePermissions |= BlobSasPermissions.Read;

        if (permissions.HasFlag(SasPermissions.Write))
            azurePermissions |= BlobSasPermissions.Write;

        if (permissions.HasFlag(SasPermissions.Delete))
            azurePermissions |= BlobSasPermissions.Delete;

        if (permissions.HasFlag(SasPermissions.List))
            azurePermissions |= BlobSasPermissions.List;

        if (permissions.HasFlag(SasPermissions.Add))
            azurePermissions |= BlobSasPermissions.Add;

        if (permissions.HasFlag(SasPermissions.Create))
            azurePermissions |= BlobSasPermissions.Create;

        if (permissions.HasFlag(SasPermissions.Update))
            azurePermissions |= BlobSasPermissions.Write; // Update maps to Write in Azure

        if (permissions.HasFlag(SasPermissions.Process))
            azurePermissions |= BlobSasPermissions.Read; // Process typically requires read

        if (permissions.HasFlag(SasPermissions.Tag))
            azurePermissions |= BlobSasPermissions.Tag;

        if (permissions.HasFlag(SasPermissions.Filter))
            azurePermissions |= BlobSasPermissions.Filter;

        return azurePermissions;
    }

    private static BlobInfo MapToBlobInfo(BlobClient blobClient, BlobProperties properties)
    {
        return new BlobInfo
        {
            Name = blobClient.Name,
            ContainerName = blobClient.BlobContainerName,
            Size = properties.ContentLength,
            ContentType = properties.ContentType,
            ETag = properties.ETag?.ToString(),
            LastModified = properties.LastModified,
            CreatedOn = properties.CreatedOn,
            Uri = blobClient.Uri.ToString(),
            Metadata = properties.Metadata ?? new Dictionary<string, string>()
        };
    }

    private static BlobInfo MapToBlobInfo(BlobClient blobClient, BlobItem blobItem)
    {
        return new BlobInfo
        {
            Name = blobItem.Name,
            ContainerName = blobClient.BlobContainerName,
            Size = blobItem.Properties.ContentLength ?? 0,
            ContentType = blobItem.Properties.ContentType,
            ETag = blobItem.Properties.ETag?.ToString(),
            LastModified = blobItem.Properties.LastModified,
            CreatedOn = blobItem.Properties.CreatedOn,
            Uri = blobClient.Uri.ToString(),
            Metadata = blobItem.Metadata ?? new Dictionary<string, string>()
        };
    }
}
