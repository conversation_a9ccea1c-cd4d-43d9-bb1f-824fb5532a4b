namespace FileService.Core.Models;

/// <summary>
/// Represents the response containing a generated SAS token
/// </summary>
public class SasTokenResponse
{
    /// <summary>
    /// The generated SAS token
    /// </summary>
    public string SasToken { get; set; } = string.Empty;

    /// <summary>
    /// The full URI with the SAS token
    /// </summary>
    public string SasUri { get; set; } = string.Empty;

    /// <summary>
    /// The expiry time of the SAS token
    /// </summary>
    public DateTimeOffset ExpiresOn { get; set; }

    /// <summary>
    /// The start time of the SAS token (if specified)
    /// </summary>
    public DateTimeOffset? StartsOn { get; set; }

    /// <summary>
    /// The permissions granted by the SAS token
    /// </summary>
    public SasPermissions Permissions { get; set; }

    /// <summary>
    /// The container name the SAS token is for
    /// </summary>
    public string ContainerName { get; set; } = string.Empty;

    /// <summary>
    /// The blob name the SAS token is for (if blob-level SAS)
    /// </summary>
    public string? BlobName { get; set; }
}
