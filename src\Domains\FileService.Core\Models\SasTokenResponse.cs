namespace FileService.Core.Models;

/// <summary>
/// Represents the response containing a generated read-only SAS token
/// </summary>
public class SasTokenResponse
{
    /// <summary>
    /// The generated SAS token
    /// </summary>
    public string SasToken { get; set; } = string.Empty;

    /// <summary>
    /// The full URI with the SAS token
    /// </summary>
    public string SasUri { get; set; } = string.Empty;

    /// <summary>
    /// The expiry time of the SAS token
    /// </summary>
    public DateTimeOffset ExpiresOn { get; set; }

    /// <summary>
    /// The project name used for this SAS token
    /// </summary>
    public string ProjectName { get; set; } = string.Empty;

    /// <summary>
    /// The actual Azure storage account name used for this SAS token
    /// </summary>
    public string StorageAccountName { get; set; } = string.Empty;

    /// <summary>
    /// The container name the SAS token is for
    /// </summary>
    public string ContainerName { get; set; } = string.Empty;

    /// <summary>
    /// The blob name the SAS token is for (if blob-level SAS)
    /// </summary>
    public string? BlobName { get; set; }
}
