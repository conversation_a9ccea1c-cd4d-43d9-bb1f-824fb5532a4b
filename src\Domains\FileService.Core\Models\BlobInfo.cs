namespace FileService.Core.Models;

/// <summary>
/// Represents information about a blob in Azure Storage
/// </summary>
public class BlobInfo
{
    /// <summary>
    /// The name of the blob
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// The container name containing the blob
    /// </summary>
    public string ContainerName { get; set; } = string.Empty;

    /// <summary>
    /// The size of the blob in bytes
    /// </summary>
    public long Size { get; set; }

    /// <summary>
    /// The content type of the blob
    /// </summary>
    public string? ContentType { get; set; }

    /// <summary>
    /// The ETag of the blob
    /// </summary>
    public string? ETag { get; set; }

    /// <summary>
    /// The last modified date of the blob
    /// </summary>
    public DateTimeOffset? LastModified { get; set; }

    /// <summary>
    /// The creation date of the blob
    /// </summary>
    public DateTimeOffset? CreatedOn { get; set; }

    /// <summary>
    /// The full URI of the blob
    /// </summary>
    public string Uri { get; set; } = string.Empty;

    /// <summary>
    /// Custom metadata associated with the blob
    /// </summary>
    public Dictionary<string, string> Metadata { get; set; } = new();
}
