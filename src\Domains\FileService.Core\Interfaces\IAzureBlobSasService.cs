using FileService.Core.Models;

namespace FileService.Core.Interfaces;

/// <summary>
/// Interface for Azure Blob Storage SAS token generation
/// </summary>
public interface IAzureBlobSasService
{
    /// <summary>
    /// Generates a SAS token for the specified blob or container
    /// </summary>
    /// <param name="request">The SAS token request parameters</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The generated SAS token response</returns>
    Task<SasTokenResponse> GenerateSasTokenAsync(SasTokenRequest request, CancellationToken cancellationToken = default);
}
