﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>
    
    <ItemGroup>
        <PackageReference Include="Azure.Storage.Blobs" Version="12.23.0" />
        <PackageReference Include="Microsoft.AspNetCore.StaticFiles" Version="2.2.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Abstractions" Version="7.0.20" />
        <PackageReference Include="Microsoft.Extensions.Options" Version="8.0.2" />
        <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="8.0.0" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\Domains\FileService.Core\FileService.Core.csproj" />
    </ItemGroup>

</Project>
