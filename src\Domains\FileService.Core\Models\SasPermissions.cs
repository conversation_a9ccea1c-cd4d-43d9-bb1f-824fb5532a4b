namespace FileService.Core.Models;

/// <summary>
/// Represents the permissions that can be granted with a SAS token
/// </summary>
[Flags]
public enum SasPermissions
{
    /// <summary>
    /// No permissions
    /// </summary>
    None = 0,

    /// <summary>
    /// Read permission
    /// </summary>
    Read = 1,

    /// <summary>
    /// Write permission
    /// </summary>
    Write = 2,

    /// <summary>
    /// Delete permission
    /// </summary>
    Delete = 4,

    /// <summary>
    /// List permission
    /// </summary>
    List = 8,

    /// <summary>
    /// Add permission
    /// </summary>
    Add = 16,

    /// <summary>
    /// Create permission
    /// </summary>
    Create = 32,

    /// <summary>
    /// Update permission
    /// </summary>
    Update = 64,

    /// <summary>
    /// Process permission
    /// </summary>
    Process = 128,

    /// <summary>
    /// Tag permission
    /// </summary>
    Tag = 256,

    /// <summary>
    /// Filter permission
    /// </summary>
    Filter = 512,

    /// <summary>
    /// All permissions
    /// </summary>
    All = Read | Write | Delete | List | Add | Create | Update | Process | Tag | Filter
}
