using Azure.Storage.Blobs;
using Azure.Storage.Sas;
using FileService.Core.Configuration;
using FileService.Core.Interfaces;
using FileService.Core.Models;
using Microsoft.Extensions.Options;

namespace FileService.HttpClients.Services;

/// <summary>
/// Implementation of Azure Blob Storage SAS token generation service
/// </summary>
public class AzureBlobSasService : IAzureBlobSasService
{
    private readonly BlobServiceClient _blobServiceClient;
    private readonly AzureStorageOptions _options;

    public AzureBlobSasService(IOptions<AzureStorageOptions> options)
    {
        _options = options.Value ?? throw new ArgumentNullException(nameof(options));
        
        if (!_options.IsValid())
            throw new InvalidOperationException("Invalid Azure Storage configuration");

        _blobServiceClient = CreateBlobServiceClient();
    }

    /// <summary>
    /// Generates a read-only SAS token for the specified blob or container
    /// </summary>
    public async Task<SasTokenResponse> GenerateSasTokenAsync(SasTokenRequest request, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(request);

        if (string.IsNullOrEmpty(request.ContainerName))
            throw new ArgumentException("Container name is required", nameof(request));

        // Validate expiry time
        var maxExpiry = DateTimeOffset.UtcNow.AddHours(_options.MaxSasExpiryHours);
        if (request.ExpiresOn > maxExpiry)
            throw new ArgumentException($"SAS token expiry cannot exceed {_options.MaxSasExpiryHours} hours", nameof(request));

        var containerClient = _blobServiceClient.GetBlobContainerClient(request.ContainerName);

        if (!string.IsNullOrEmpty(request.BlobName))
        {
            // Generate blob-level read SAS
            return GenerateBlobSasToken(containerClient, request);
        }
        else
        {
            // Generate container-level read SAS
            return GenerateContainerSasToken(containerClient, request);
        }
    }

    private BlobServiceClient CreateBlobServiceClient()
    {
        if (!string.IsNullOrEmpty(_options.ConnectionString))
        {
            return new BlobServiceClient(_options.ConnectionString);
        }

        if (!string.IsNullOrEmpty(_options.AccountName))
        {
            var serviceUri = _options.GetBlobServiceUri();
            if (serviceUri == null)
                throw new InvalidOperationException("Unable to determine blob service URI");

            if (!string.IsNullOrEmpty(_options.AccountKey))
            {
                var credential = new Azure.Storage.StorageSharedKeyCredential(_options.AccountName, _options.AccountKey);
                return new BlobServiceClient(serviceUri, credential);
            }

            if (!string.IsNullOrEmpty(_options.SasToken))
            {
                var sasUri = new UriBuilder(serviceUri) { Query = _options.SasToken }.Uri;
                return new BlobServiceClient(sasUri);
            }
        }

        throw new InvalidOperationException("Invalid Azure Storage configuration");
    }

    private SasTokenResponse GenerateBlobSasToken(BlobContainerClient containerClient, SasTokenRequest request)
    {
        var blobClient = containerClient.GetBlobClient(request.BlobName!);

        if (!blobClient.CanGenerateSasUri)
            throw new InvalidOperationException("Cannot generate SAS token with current authentication method");

        var sasBuilder = new BlobSasBuilder
        {
            BlobContainerName = request.ContainerName,
            BlobName = request.BlobName,
            Resource = "b", // blob
            ExpiresOn = request.ExpiresOn
        };

        // Set read-only permissions
        sasBuilder.SetPermissions(BlobSasPermissions.Read);

        var sasToken = blobClient.GenerateSasUri(sasBuilder).Query;
        var sasUri = blobClient.Uri + sasToken;

        return new SasTokenResponse
        {
            SasToken = sasToken,
            SasUri = sasUri,
            ExpiresOn = request.ExpiresOn,
            ContainerName = request.ContainerName,
            BlobName = request.BlobName
        };
    }

    private SasTokenResponse GenerateContainerSasToken(BlobContainerClient containerClient, SasTokenRequest request)
    {
        if (!containerClient.CanGenerateSasUri)
            throw new InvalidOperationException("Cannot generate SAS token with current authentication method");

        var sasBuilder = new BlobSasBuilder
        {
            BlobContainerName = request.ContainerName,
            Resource = "c", // container
            ExpiresOn = request.ExpiresOn
        };

        // Set read and list permissions for container
        sasBuilder.SetPermissions(BlobSasPermissions.Read | BlobSasPermissions.List);

        var sasToken = containerClient.GenerateSasUri(sasBuilder).Query;
        var sasUri = containerClient.Uri + sasToken;

        return new SasTokenResponse
        {
            SasToken = sasToken,
            SasUri = sasUri,
            ExpiresOn = request.ExpiresOn,
            ContainerName = request.ContainerName
        };
    }


}
