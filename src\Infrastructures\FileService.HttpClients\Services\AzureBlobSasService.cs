using Azure.Storage.Blobs;
using Azure.Storage.Sas;
using FileService.Core.Configuration;
using FileService.Core.Interfaces;
using FileService.Core.Models;
using Microsoft.Extensions.Options;

namespace FileService.HttpClients.Services;

/// <summary>
/// Implementation of Azure Blob Storage SAS token generation service with support for multiple storage accounts
/// </summary>
public class AzureBlobSasService : IAzureBlobSasService
{
    private readonly AzureStorageOptions _options;
    private readonly Dictionary<string, BlobServiceClient> _blobServiceClients;

    public AzureBlobSasService(IOptions<AzureStorageOptions> options)
    {
        _options = options.Value ?? throw new ArgumentNullException(nameof(options));

        if (!_options.IsValid())
            throw new InvalidOperationException("Invalid Azure Storage configuration");

        _blobServiceClients = new Dictionary<string, BlobServiceClient>();
    }

    /// <summary>
    /// Generates a read-only SAS token for the specified blob or container
    /// </summary>
    public async Task<SasTokenResponse> GenerateSasTokenAsync(SasTokenRequest request, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(request);

        if (string.IsNullOrEmpty(request.StorageAccountName))
            throw new ArgumentException("Storage account name is required", nameof(request));

        if (string.IsNullOrEmpty(request.ContainerName))
            throw new ArgumentException("Container name is required", nameof(request));

        // Validate expiry time
        var maxExpiry = DateTimeOffset.UtcNow.AddHours(_options.MaxSasExpiryHours);
        if (request.ExpiresOn > maxExpiry)
            throw new ArgumentException($"SAS token expiry cannot exceed {_options.MaxSasExpiryHours} hours", nameof(request));

        // Get or create the blob service client for the specified storage account
        var blobServiceClient = GetBlobServiceClient(request.StorageAccountName);

        // Validate that the container is configured for this storage account
        var storageAccount = _options.GetStorageAccount(request.StorageAccountName);
        if (storageAccount != null && storageAccount.Containers.Count > 0 &&
            !storageAccount.HasContainer(request.ContainerName))
        {
            throw new ArgumentException($"Container '{request.ContainerName}' is not configured for storage account '{request.StorageAccountName}'", nameof(request));
        }

        var containerClient = blobServiceClient.GetBlobContainerClient(request.ContainerName);

        if (!string.IsNullOrEmpty(request.BlobName))
        {
            // Generate blob-level read SAS
            return GenerateBlobSasToken(containerClient, request);
        }
        else
        {
            // Generate container-level read SAS
            return GenerateContainerSasToken(containerClient, request);
        }
    }

    /// <summary>
    /// Gets or creates a BlobServiceClient for the specified storage account
    /// </summary>
    /// <param name="storageAccountName">The storage account name</param>
    /// <returns>BlobServiceClient for the specified storage account</returns>
    private BlobServiceClient GetBlobServiceClient(string storageAccountName)
    {
        // Check if we already have a client for this storage account
        if (_blobServiceClients.TryGetValue(storageAccountName, out var existingClient))
        {
            return existingClient;
        }

        // Get the storage account configuration
        var storageAccount = _options.GetStorageAccount(storageAccountName);
        if (storageAccount == null)
        {
            throw new ArgumentException($"Storage account '{storageAccountName}' not found in configuration", nameof(storageAccountName));
        }

        // Create a new client for this storage account
        var client = CreateBlobServiceClient(storageAccount);

        // Cache the client for future use
        _blobServiceClients[storageAccountName] = client;

        return client;
    }

    /// <summary>
    /// Creates a BlobServiceClient from storage account configuration
    /// </summary>
    /// <param name="storageAccount">The storage account configuration</param>
    /// <returns>BlobServiceClient</returns>
    private static BlobServiceClient CreateBlobServiceClient(StorageAccountConfig storageAccount)
    {
        if (!string.IsNullOrEmpty(storageAccount.ConnectionString))
        {
            return new BlobServiceClient(storageAccount.ConnectionString);
        }

        if (!string.IsNullOrEmpty(storageAccount.AccountName))
        {
            var serviceUri = storageAccount.GetBlobServiceUri();
            if (serviceUri == null)
                throw new InvalidOperationException("Unable to determine blob service URI");

            if (!string.IsNullOrEmpty(storageAccount.AccountKey))
            {
                var credential = new Azure.Storage.StorageSharedKeyCredential(storageAccount.AccountName, storageAccount.AccountKey);
                return new BlobServiceClient(serviceUri, credential);
            }

            if (!string.IsNullOrEmpty(storageAccount.SasToken))
            {
                var sasUri = new UriBuilder(serviceUri) { Query = storageAccount.SasToken }.Uri;
                return new BlobServiceClient(sasUri);
            }
        }

        throw new InvalidOperationException($"Invalid Azure Storage configuration for account '{storageAccount.AccountName}'");
    }

    private SasTokenResponse GenerateBlobSasToken(BlobContainerClient containerClient, SasTokenRequest request)
    {
        var blobClient = containerClient.GetBlobClient(request.BlobName!);

        if (!blobClient.CanGenerateSasUri)
            throw new InvalidOperationException("Cannot generate SAS token with current authentication method");

        var sasBuilder = new BlobSasBuilder
        {
            BlobContainerName = request.ContainerName,
            BlobName = request.BlobName,
            Resource = "b", // blob
            ExpiresOn = request.ExpiresOn
        };

        // Set read-only permissions
        sasBuilder.SetPermissions(BlobSasPermissions.Read);

        var sasToken = blobClient.GenerateSasUri(sasBuilder).Query;
        var sasUri = blobClient.Uri + sasToken;

        return new SasTokenResponse
        {
            SasToken = sasToken,
            SasUri = sasUri,
            ExpiresOn = request.ExpiresOn,
            StorageAccountName = request.StorageAccountName,
            ContainerName = request.ContainerName,
            BlobName = request.BlobName
        };
    }

    private SasTokenResponse GenerateContainerSasToken(BlobContainerClient containerClient, SasTokenRequest request)
    {
        if (!containerClient.CanGenerateSasUri)
            throw new InvalidOperationException("Cannot generate SAS token with current authentication method");

        var sasBuilder = new BlobSasBuilder
        {
            BlobContainerName = request.ContainerName,
            Resource = "c", // container
            ExpiresOn = request.ExpiresOn
        };

        // Set read and list permissions for container
        sasBuilder.SetPermissions(BlobSasPermissions.Read | BlobSasPermissions.List);

        var sasToken = containerClient.GenerateSasUri(sasBuilder).Query;
        var sasUri = containerClient.Uri + sasToken;

        return new SasTokenResponse
        {
            SasToken = sasToken,
            SasUri = sasUri,
            ExpiresOn = request.ExpiresOn,
            StorageAccountName = request.StorageAccountName,
            ContainerName = request.ContainerName
        };
    }


}
