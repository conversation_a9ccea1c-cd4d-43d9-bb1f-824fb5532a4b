namespace FileService.Core.Models;

/// <summary>
/// Represents a request to generate a read-only SAS token for Azure Blob Storage
/// </summary>
public class SasTokenRequest
{
    /// <summary>
    /// The project name to use (must match configuration key, e.g., "rehash", "reconnect")
    /// </summary>
    public string ProjectName { get; set; } = string.Empty;

    /// <summary>
    /// The name of the blob container
    /// </summary>
    public string ContainerName { get; set; } = string.Empty;

    /// <summary>
    /// The name of the blob (optional, if not provided, container-level SAS is generated)
    /// </summary>
    public string? BlobName { get; set; }

    /// <summary>
    /// The expiry time for the SAS token (default: 1 hour from now)
    /// </summary>
    public DateTimeOffset ExpiresOn { get; set; } = DateTimeOffset.UtcNow.AddHours(1);
}
