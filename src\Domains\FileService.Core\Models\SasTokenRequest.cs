namespace FileService.Core.Models;

/// <summary>
/// Represents a request to generate a SAS token for Azure Blob Storage
/// </summary>
public class SasTokenRequest
{
    /// <summary>
    /// The name of the blob container
    /// </summary>
    public string ContainerName { get; set; } = string.Empty;

    /// <summary>
    /// The name of the blob (optional, if not provided, container-level SAS is generated)
    /// </summary>
    public string? BlobName { get; set; }

    /// <summary>
    /// The permissions to grant with the SAS token
    /// </summary>
    public SasPermissions Permissions { get; set; } = SasPermissions.Read;

    /// <summary>
    /// The expiry time for the SAS token (default: 1 hour from now)
    /// </summary>
    public DateTimeOffset ExpiresOn { get; set; } = DateTimeOffset.UtcNow.AddHours(1);

    /// <summary>
    /// The start time for the SAS token (optional)
    /// </summary>
    public DateTimeOffset? StartsOn { get; set; }

    /// <summary>
    /// Content type to set for the blob (optional)
    /// </summary>
    public string? ContentType { get; set; }

    /// <summary>
    /// Content disposition to set for the blob (optional)
    /// </summary>
    public string? ContentDisposition { get; set; }
}
