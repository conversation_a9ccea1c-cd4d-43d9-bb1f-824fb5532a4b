using FileService.Core.Configuration;
using FileService.Core.Interfaces;
using FileService.Core.Models;
using FileService.Services.Interfaces;
using Microsoft.Extensions.Options;
using System.Text.RegularExpressions;

namespace FileService.Services.Services;

/// <summary>
/// Service for file blob operations with storage validation
/// </summary>
public class FileBlobService : IFileBlobService
{
    private readonly IAzureBlobSasService _azureBlobSasService;
    private readonly AzureStorageOptions _storageOptions;

    // Regex pattern to match Azure blob storage URLs
    private static readonly Regex AzureBlobUrlRegex = new(@"^https:\/\/\w+\.blob\.core\.windows\.net\/([\w-]+)\/(.+)$", RegexOptions.Compiled);

    public FileBlobService(IAzureBlobSasService azureBlobSasService, IOptions<AzureStorageOptions> storageOptions)
    {
        _azureBlobSasService = azureBlobSasService;
        _storageOptions = storageOptions.Value;
    }

    /// <summary>
    /// Generates a read-only SAS token for the specified blob URL
    /// </summary>
    public async Task<SasTokenResponse> GenerateSasTokenAsync(string blobUrl, int expiryHours = 1, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(blobUrl))
            throw new ArgumentException("Blob URL is required", nameof(blobUrl));

        if (expiryHours <= 0 || expiryHours > _storageOptions.MaxSasExpiryHours)
            throw new ArgumentException($"Expiry hours must be between 1 and {_storageOptions.MaxSasExpiryHours}", nameof(expiryHours));

        // Parse the blob URL
        var urlInfo = ParseBlobUrl(blobUrl);

        // Find the project that matches this storage account
        var projectName = FindProjectByStorageAccount(urlInfo.StorageAccountName);
        if (string.IsNullOrEmpty(projectName))
        {
            throw new ArgumentException($"Storage account '{urlInfo.StorageAccountName}' is not configured in any project", nameof(blobUrl));
        }

        // Validate that the container is configured for this project
        if (!IsContainerConfigured(projectName, urlInfo.ContainerName))
        {
            throw new ArgumentException($"Container '{urlInfo.ContainerName}' is not configured for project '{projectName}'", nameof(blobUrl));
        }

        // Build the SAS token request
        var sasRequest = new SasTokenRequest
        {
            ProjectName = projectName,
            ContainerName = urlInfo.ContainerName,
            BlobName = urlInfo.BlobName,
            ExpiresOn = DateTimeOffset.UtcNow.AddHours(expiryHours)
        };

        // Delegate to the Azure Blob SAS service
        var sasResponse = await _azureBlobSasService.GenerateSasTokenAsync(sasRequest, cancellationToken);

        // Apply custom domain transformation if configured
        sasResponse.SasUri = ApplyCustomDomain(sasResponse.SasUri, projectName, urlInfo.ContainerName);

        return sasResponse;
    }

    /// <summary>
    /// Gets all available project names configured in the system
    /// </summary>
    public Task<IEnumerable<string>> GetAvailableProjectsAsync()
    {
        var projects = _storageOptions.GetStorageAccountNames();
        return Task.FromResult(projects);
    }

    /// <summary>
    /// Gets all containers configured for a specific project
    /// </summary>
    public Task<IEnumerable<string>> GetProjectContainersAsync(string projectName)
    {
        if (string.IsNullOrEmpty(projectName))
            throw new ArgumentException("Project name is required", nameof(projectName));

        var storageAccount = _storageOptions.GetStorageAccount(projectName);
        if (storageAccount == null)
            throw new ArgumentException($"Project '{projectName}' not found", nameof(projectName));

        return Task.FromResult<IEnumerable<string>>(storageAccount.Containers);
    }

    /// <summary>
    /// Checks if a project is configured in the system
    /// </summary>
    public bool IsProjectConfigured(string projectName)
    {
        if (string.IsNullOrEmpty(projectName))
            return false;

        return _storageOptions.GetStorageAccount(projectName) != null;
    }

    /// <summary>
    /// Checks if a container is configured for a specific project
    /// </summary>
    public bool IsContainerConfigured(string projectName, string containerName)
    {
        if (string.IsNullOrEmpty(projectName) || string.IsNullOrEmpty(containerName))
            return false;

        var storageAccount = _storageOptions.GetStorageAccount(projectName);
        if (storageAccount == null)
            return false;

        // If no containers are configured, allow all containers
        if (storageAccount.Containers.Count == 0)
            return true;

        return storageAccount.HasContainer(containerName);
    }

    /// <summary>
    /// Parses an Azure blob URL to extract storage account, container, and blob name
    /// </summary>
    private static BlobUrlInfo ParseBlobUrl(string blobUrl)
    {
        if (!Uri.TryCreate(blobUrl, UriKind.Absolute, out var uri))
            throw new ArgumentException("Invalid blob URL format", nameof(blobUrl));

        // Expected format: https://storageaccount.blob.core.windows.net/container/blob/path
        var host = uri.Host;
        if (!host.EndsWith(".blob.core.windows.net", StringComparison.OrdinalIgnoreCase))
            throw new ArgumentException("URL must be an Azure blob storage URL", nameof(blobUrl));

        // Extract storage account name from host
        var storageAccountName = host.Substring(0, host.IndexOf('.'));

        // Extract container and blob path from the path
        var pathSegments = uri.AbsolutePath.TrimStart('/').Split('/', 2);
        if (pathSegments.Length < 2)
            throw new ArgumentException("URL must include container and blob name", nameof(blobUrl));

        var containerName = pathSegments[0];
        var blobName = pathSegments[1];

        return new BlobUrlInfo
        {
            StorageAccountName = storageAccountName,
            ContainerName = containerName,
            BlobName = blobName
        };
    }

    /// <summary>
    /// Finds the project name that matches the given storage account name
    /// </summary>
    private string? FindProjectByStorageAccount(string storageAccountName)
    {
        foreach (var kvp in _storageOptions.StorageAccounts)
        {
            if (string.Equals(kvp.Value.StorageAccountName, storageAccountName, StringComparison.OrdinalIgnoreCase))
            {
                return kvp.Key;
            }
        }
        return null;
    }

    /// <summary>
    /// Applies custom domain transformation to the SAS URI if configured
    /// </summary>
    /// <param name="sasUri">The original SAS URI from Azure</param>
    /// <param name="projectName">The project name</param>
    /// <param name="containerName">The container name</param>
    /// <returns>The transformed URI with custom domain if configured, otherwise the original URI</returns>
    private string ApplyCustomDomain(string sasUri, string projectName, string containerName)
    {
        // Get the storage account configuration for this project
        var storageAccount = _storageOptions.GetStorageAccount(projectName);
        if (storageAccount == null || !storageAccount.HasCustomDomain(containerName))
        {
            // No custom domain configured, return original URI
            return sasUri;
        }

        var customDomain = storageAccount.GetCustomDomain(containerName);
        if (string.IsNullOrEmpty(customDomain))
        {
            return sasUri;
        }

        // Apply regex transformation
        // Pattern: ^https:\/\/\w+\.blob\.core\.windows\.net\/([\w-]+)\/(.+)$
        // Group 1: container name
        // Group 2: blob path with SAS token
        var match = AzureBlobUrlRegex.Match(sasUri);
        if (!match.Success)
        {
            // URL doesn't match expected pattern, return original
            return sasUri;
        }

        var capturedContainer = match.Groups[1].Value;
        var blobPathWithSas = match.Groups[2].Value;

        // Verify the captured container matches the expected container
        if (!string.Equals(capturedContainer, containerName, StringComparison.OrdinalIgnoreCase))
        {
            // Container mismatch, return original URI
            return sasUri;
        }

        // Build the custom domain URL
        // Remove trailing slash from custom domain if present
        var cleanCustomDomain = customDomain.TrimEnd('/');

        // Combine custom domain with blob path and SAS token
        return $"{cleanCustomDomain}/{blobPathWithSas}";
    }

    /// <summary>
    /// Helper class to hold parsed blob URL information
    /// </summary>
    private class BlobUrlInfo
    {
        public string StorageAccountName { get; set; } = string.Empty;
        public string ContainerName { get; set; } = string.Empty;
        public string BlobName { get; set; } = string.Empty;
    }
}
