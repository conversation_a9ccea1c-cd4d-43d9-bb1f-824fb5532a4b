# Azure Blob SAS Token Service

A simple .NET 8 service for generating read-only SAS (Shared Access Signature) tokens for Azure Blob Storage with support for multiple storage accounts.

## Features

- **Multiple Storage Accounts**: Support for multiple named storage accounts with different connection strings
- **Container Validation**: Validate that containers are configured for specific storage accounts
- **Dynamic Client Creation**: Automatically create and cache BlobServiceClient instances for each storage account
- Generate read-only SAS tokens for individual blobs
- Generate read-only SAS tokens for containers (with list permissions)
- Clean architecture with separated concerns
- Simple REST API endpoints
- Configurable token expiry times

## Project Structure

```
src/
├── Domains/
│   └── FileService.Core/           # Domain models and interfaces
│       ├── Configuration/          # Configuration models
│       ├── Interfaces/            # Service interfaces
│       └── Models/                # Domain models
├── Infrastructures/
│   └── FileService.HttpClients/   # Azure SDK implementation
│       ├── Extensions/            # DI registration extensions
│       └── Services/              # Service implementations
└── Applications/
    └── WebApi/
        └── FileService.Api/       # REST API
            └── Controllers/       # API controllers
```

## Configuration

Configure multiple Azure Storage accounts in `appsettings.json`:

```json
{
  "AzureStorage": {
    "DefaultSasExpiryHours": 1,
    "MaxSasExpiryHours": 24,
    "StorageAccounts": {
      "development": {
        "ConnectionString": "UseDevelopmentStorage=true",
        "Containers": ["documents", "images", "backups"]
      },
      "production": {
        "ConnectionString": "DefaultEndpointsProtocol=https;AccountName=prodaccount;AccountKey=your-key;EndpointSuffix=core.windows.net",
        "Containers": ["documents", "images", "logs", "reports"]
      },
      "archive": {
        "ConnectionString": "DefaultEndpointsProtocol=https;AccountName=archiveaccount;AccountKey=your-key;EndpointSuffix=core.windows.net",
        "Containers": ["archived-documents", "old-backups"]
      }
    }
  }
}
```

### Configuration Structure

- **StorageAccounts**: A dictionary of named storage accounts
- **Key**: The storage account name (used in API calls)
- **ConnectionString**: Azure Storage connection string for the account
- **Containers**: List of containers available in this storage account (optional, used for validation)

### Alternative Configuration Options

You can also configure using individual properties instead of a connection string:

```json
{
  "AzureStorage": {
    "DefaultSasExpiryHours": 1,
    "MaxSasExpiryHours": 24,
    "StorageAccounts": {
      "myaccount": {
        "AccountName": "your-storage-account",
        "AccountKey": "your-account-key",
        "Containers": ["container1", "container2"]
      }
    }
  }
}
```

## API Endpoints

### Get Storage Accounts Information

Get information about configured storage accounts and their containers:

```
GET /api/sastoken/accounts
```

**Response:**
```json
{
  "totalAccounts": 3,
  "storageAccounts": [
    {
      "storageAccountName": "development",
      "containers": ["documents", "images", "backups"],
      "containerCount": 3
    },
    {
      "storageAccountName": "production",
      "containers": ["documents", "images", "logs", "reports"],
      "containerCount": 4
    },
    {
      "storageAccountName": "archive",
      "containers": ["archived-documents", "old-backups"],
      "containerCount": 2
    }
  ]
}
```

### Generate Blob SAS Token

Generate a read-only SAS token for a specific blob:

```
GET /api/sastoken/blob?storageAccountName=production&containerName=documents&blobName=myfile.txt&expiryHours=2
```

**Parameters:**
- `storageAccountName` (required): The storage account name (must match configuration)
- `containerName` (required): The name of the blob container
- `blobName` (required): The name of the blob
- `expiryHours` (optional): Hours until token expires (default: 1)

**Response:**
```json
{
  "sasToken": "?sv=2022-11-02&sr=b&sig=...",
  "sasUri": "https://prodaccount.blob.core.windows.net/documents/myfile.txt?sv=2022-11-02&sr=b&sig=...",
  "expiresOn": "2024-01-01T12:00:00Z",
  "storageAccountName": "production",
  "containerName": "documents",
  "blobName": "myfile.txt"
}
```

### Generate Container SAS Token

Generate a read-only SAS token for a container (includes list permissions):

```
GET /api/sastoken/container?storageAccountName=production&containerName=documents&expiryHours=2
```

**Parameters:**
- `storageAccountName` (required): The storage account name (must match configuration)
- `containerName` (required): The name of the blob container
- `expiryHours` (optional): Hours until token expires (default: 1)

**Response:**
```json
{
  "sasToken": "?sv=2022-11-02&sr=c&sig=...",
  "sasUri": "https://prodaccount.blob.core.windows.net/documents?sv=2022-11-02&sr=c&sig=...",
  "expiresOn": "2024-01-01T12:00:00Z",
  "storageAccountName": "production",
  "containerName": "documents"
}
```

## Usage in Code

### Register the Service

```csharp
// In Program.cs
builder.Services.AddAzureBlobStorage(builder.Configuration);
```

### Inject and Use the Service

```csharp
public class MyService
{
    private readonly IAzureBlobSasService _sasService;

    public MyService(IAzureBlobSasService sasService)
    {
        _sasService = sasService;
    }

    public async Task<string> GetBlobSasUrl(string storageAccount, string container, string blob)
    {
        var request = new SasTokenRequest
        {
            StorageAccountName = storageAccount,
            ContainerName = container,
            BlobName = blob,
            ExpiresOn = DateTimeOffset.UtcNow.AddHours(2)
        };

        var response = await _sasService.GenerateSasTokenAsync(request);
        return response.SasUri;
    }
}
```

## Running the Application

1. Configure your Azure Storage accounts in `appsettings.json`
2. Run the application:
   ```bash
   dotnet run --project src/Applications/WebApi/FileService.Api
   ```
3. Navigate to `https://localhost:7072/swagger` to test the API
4. Use the `/api/sastoken/accounts` endpoint to see available storage accounts and containers

## Example Usage Flow

1. **Check available accounts**: `GET /api/sastoken/accounts`
2. **Generate blob SAS**: `GET /api/sastoken/blob?storageAccountName=production&containerName=documents&blobName=file.pdf`
3. **Use the SAS URL** to access the blob directly from your application

## Security Notes

- This service only generates read-only SAS tokens
- Token expiry times are configurable and enforced
- Maximum expiry time is limited by configuration
- Container validation ensures only configured containers can be accessed
- Each storage account is isolated with its own connection string
- Use HTTPS in production environments
- Store connection strings securely (Azure Key Vault recommended)
- Consider using Azure Managed Identity for production deployments

## Benefits of Multiple Storage Account Support

- **Environment Separation**: Use different storage accounts for dev/staging/production
- **Data Segregation**: Separate sensitive data across different storage accounts
- **Performance**: Distribute load across multiple storage accounts
- **Cost Management**: Different storage tiers and billing for different data types
- **Security**: Granular access control per storage account
- **Compliance**: Meet regulatory requirements with data isolation
