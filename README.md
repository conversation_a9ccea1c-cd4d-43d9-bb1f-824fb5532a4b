# Azure Blob SAS Token Service

A simple .NET 8 service for generating read-only SAS (Shared Access Signature) tokens for Azure Blob Storage.

## Features

- Generate read-only SAS tokens for individual blobs
- Generate read-only SAS tokens for containers (with list permissions)
- Clean architecture with separated concerns
- Simple REST API endpoints
- Configurable token expiry times

## Project Structure

```
src/
├── Domains/
│   └── FileService.Core/           # Domain models and interfaces
│       ├── Configuration/          # Configuration models
│       ├── Interfaces/            # Service interfaces
│       └── Models/                # Domain models
├── Infrastructures/
│   └── FileService.HttpClients/   # Azure SDK implementation
│       ├── Extensions/            # DI registration extensions
│       └── Services/              # Service implementations
└── Applications/
    └── WebApi/
        └── FileService.Api/       # REST API
            └── Controllers/       # API controllers
```

## Configuration

Add your Azure Storage configuration to `appsettings.json`:

```json
{
  "AzureStorage": {
    "ConnectionString": "DefaultEndpointsProtocol=https;AccountName=your-account;AccountKey=your-key;EndpointSuffix=core.windows.net",
    "DefaultSasExpiryHours": 1,
    "MaxSasExpiryHours": 24
  }
}
```

### Alternative Configuration Options

You can also configure using individual properties instead of a connection string:

```json
{
  "AzureStorage": {
    "AccountName": "your-storage-account",
    "AccountKey": "your-account-key",
    "DefaultSasExpiryHours": 1,
    "MaxSasExpiryHours": 24
  }
}
```

## API Endpoints

### Generate Blob SAS Token

Generate a read-only SAS token for a specific blob:

```
GET /api/sastoken/blob?containerName=mycontainer&blobName=myfile.txt&expiryHours=2
```

**Parameters:**
- `containerName` (required): The name of the blob container
- `blobName` (required): The name of the blob
- `expiryHours` (optional): Hours until token expires (default: 1)

**Response:**
```json
{
  "sasToken": "?sv=2022-11-02&sr=b&sig=...",
  "sasUri": "https://account.blob.core.windows.net/container/blob.txt?sv=2022-11-02&sr=b&sig=...",
  "expiresOn": "2024-01-01T12:00:00Z",
  "containerName": "mycontainer",
  "blobName": "myfile.txt"
}
```

### Generate Container SAS Token

Generate a read-only SAS token for a container (includes list permissions):

```
GET /api/sastoken/container?containerName=mycontainer&expiryHours=2
```

**Parameters:**
- `containerName` (required): The name of the blob container
- `expiryHours` (optional): Hours until token expires (default: 1)

**Response:**
```json
{
  "sasToken": "?sv=2022-11-02&sr=c&sig=...",
  "sasUri": "https://account.blob.core.windows.net/container?sv=2022-11-02&sr=c&sig=...",
  "expiresOn": "2024-01-01T12:00:00Z",
  "containerName": "mycontainer"
}
```

## Usage in Code

### Register the Service

```csharp
// In Program.cs
builder.Services.AddAzureBlobStorage(builder.Configuration);
```

### Inject and Use the Service

```csharp
public class MyService
{
    private readonly IAzureBlobSasService _sasService;

    public MyService(IAzureBlobSasService sasService)
    {
        _sasService = sasService;
    }

    public async Task<string> GetBlobSasUrl(string container, string blob)
    {
        var request = new SasTokenRequest
        {
            ContainerName = container,
            BlobName = blob,
            ExpiresOn = DateTimeOffset.UtcNow.AddHours(2)
        };

        var response = await _sasService.GenerateSasTokenAsync(request);
        return response.SasUri;
    }
}
```

## Running the Application

1. Configure your Azure Storage connection in `appsettings.json`
2. Run the application:
   ```bash
   dotnet run --project src/Applications/WebApi/FileService.Api
   ```
3. Navigate to `https://localhost:7072/swagger` to test the API

## Security Notes

- This service only generates read-only SAS tokens
- Token expiry times are configurable and enforced
- Maximum expiry time is limited by configuration
- Use HTTPS in production environments
- Store connection strings securely (Azure Key Vault recommended)
