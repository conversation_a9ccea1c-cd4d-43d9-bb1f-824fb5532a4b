﻿
Microsoft Visual Studio Solution File, Format Version 12.00
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{BBE64BA0-0D2C-4042-A663-6FDF33A4B807}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "test", "test", "{21B67A25-3BF8-4049-8826-E1B2551275AE}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Applications", "Applications", "{67B8C483-12A2-4223-AC3B-DF251CD765BE}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Domains", "Domains", "{28FB1E61-2CCE-482C-9640-981395AD181A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Infrastructures", "Infrastructures", "{5CCC74B9-BC6D-45D4-B5BC-57399EA5A65C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FileService.HttpClients", "src\Infrastructures\FileService.HttpClients\FileService.HttpClients.csproj", "{DCE39B26-1D34-4EAA-9E20-C37E372C0B12}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FileService.Core", "src\Domains\FileService.Core\FileService.Core.csproj", "{90678741-CEC9-417A-BACC-37A82788483C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FileService.Tests", "test\FileService.Tests\FileService.Tests.csproj", "{25673664-BD8E-46BA-B69A-3393ABEB60D0}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "WebApi", "WebApi", "{9D536CED-59CC-414C-BFC8-143E1BA1B9C5}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "WebServices", "WebServices", "{B07D6D91-5E34-415F-ABC2-576788059BFD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FileService.Services", "src\Applications\WebServices\FileService.Services\FileService.Services.csproj", "{5F8F5A4B-6C38-488E-B342-31B5343EABC6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FileService.Api", "src\Applications\WebApi\FileService.Api\FileService.Api.csproj", "{8B6CF09F-4B08-48D2-A017-123C098ED569}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{67B8C483-12A2-4223-AC3B-DF251CD765BE} = {BBE64BA0-0D2C-4042-A663-6FDF33A4B807}
		{28FB1E61-2CCE-482C-9640-981395AD181A} = {BBE64BA0-0D2C-4042-A663-6FDF33A4B807}
		{5CCC74B9-BC6D-45D4-B5BC-57399EA5A65C} = {BBE64BA0-0D2C-4042-A663-6FDF33A4B807}
		{DCE39B26-1D34-4EAA-9E20-C37E372C0B12} = {5CCC74B9-BC6D-45D4-B5BC-57399EA5A65C}
		{90678741-CEC9-417A-BACC-37A82788483C} = {28FB1E61-2CCE-482C-9640-981395AD181A}
		{25673664-BD8E-46BA-B69A-3393ABEB60D0} = {21B67A25-3BF8-4049-8826-E1B2551275AE}
		{9D536CED-59CC-414C-BFC8-143E1BA1B9C5} = {67B8C483-12A2-4223-AC3B-DF251CD765BE}
		{B07D6D91-5E34-415F-ABC2-576788059BFD} = {67B8C483-12A2-4223-AC3B-DF251CD765BE}
		{5F8F5A4B-6C38-488E-B342-31B5343EABC6} = {B07D6D91-5E34-415F-ABC2-576788059BFD}
		{8B6CF09F-4B08-48D2-A017-123C098ED569} = {9D536CED-59CC-414C-BFC8-143E1BA1B9C5}
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{DCE39B26-1D34-4EAA-9E20-C37E372C0B12}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DCE39B26-1D34-4EAA-9E20-C37E372C0B12}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DCE39B26-1D34-4EAA-9E20-C37E372C0B12}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DCE39B26-1D34-4EAA-9E20-C37E372C0B12}.Release|Any CPU.Build.0 = Release|Any CPU
		{90678741-CEC9-417A-BACC-37A82788483C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{90678741-CEC9-417A-BACC-37A82788483C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{90678741-CEC9-417A-BACC-37A82788483C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{90678741-CEC9-417A-BACC-37A82788483C}.Release|Any CPU.Build.0 = Release|Any CPU
		{25673664-BD8E-46BA-B69A-3393ABEB60D0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{25673664-BD8E-46BA-B69A-3393ABEB60D0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{25673664-BD8E-46BA-B69A-3393ABEB60D0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{25673664-BD8E-46BA-B69A-3393ABEB60D0}.Release|Any CPU.Build.0 = Release|Any CPU
		{5F8F5A4B-6C38-488E-B342-31B5343EABC6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5F8F5A4B-6C38-488E-B342-31B5343EABC6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5F8F5A4B-6C38-488E-B342-31B5343EABC6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5F8F5A4B-6C38-488E-B342-31B5343EABC6}.Release|Any CPU.Build.0 = Release|Any CPU
		{8B6CF09F-4B08-48D2-A017-123C098ED569}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8B6CF09F-4B08-48D2-A017-123C098ED569}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8B6CF09F-4B08-48D2-A017-123C098ED569}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8B6CF09F-4B08-48D2-A017-123C098ED569}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
EndGlobal
