namespace FileService.Core.Models;

/// <summary>
/// Represents a request to upload a blob to Azure Storage
/// </summary>
public class BlobUploadRequest
{
    /// <summary>
    /// The name of the container to upload to
    /// </summary>
    public string ContainerName { get; set; } = string.Empty;

    /// <summary>
    /// The name to give the blob
    /// </summary>
    public string BlobName { get; set; } = string.Empty;

    /// <summary>
    /// The content to upload
    /// </summary>
    public Stream Content { get; set; } = Stream.Null;

    /// <summary>
    /// The content type of the blob
    /// </summary>
    public string? ContentType { get; set; }

    /// <summary>
    /// Custom metadata to associate with the blob
    /// </summary>
    public Dictionary<string, string> Metadata { get; set; } = new();

    /// <summary>
    /// Whether to overwrite the blob if it already exists
    /// </summary>
    public bool Overwrite { get; set; } = false;

    /// <summary>
    /// Access tier for the blob (Hot, Cool, Archive)
    /// </summary>
    public string? AccessTier { get; set; }
}
