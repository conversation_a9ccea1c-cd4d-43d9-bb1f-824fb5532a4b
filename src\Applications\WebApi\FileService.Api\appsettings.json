{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "AzureStorage": {"DefaultSasExpiryHours": 1, "MaxSasExpiryHours": 24, "StorageAccounts": {"development": {"ConnectionString": "UseDevelopmentStorage=true", "Containers": ["documents", "images", "backups"]}, "production": {"ConnectionString": "DefaultEndpointsProtocol=https;AccountName=prodaccount;AccountKey=your-key;EndpointSuffix=core.windows.net", "Containers": ["documents", "images", "logs", "reports"]}, "archive": {"ConnectionString": "DefaultEndpointsProtocol=https;AccountName=archiveaccount;AccountKey=your-key;EndpointSuffix=core.windows.net", "Containers": ["archived-documents", "old-backups"]}}}}