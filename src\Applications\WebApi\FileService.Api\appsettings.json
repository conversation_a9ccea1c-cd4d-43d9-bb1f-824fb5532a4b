{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "AzureStorage": {"DefaultSasExpiryHours": 1, "MaxSasExpiryHours": 24, "StorageAccounts": {"rehash": {"ConnectionString": "DefaultEndpointsProtocol=https;AccountName=rehash3dev;AccountKey=your-account-key;EndpointSuffix=core.windows.net", "StorageAccountName": "rehash3dev", "Containers": ["files", "documents", "images"], "CustomDomains": {"files": "http://friendly-url.domain.com"}}, "reconnect": {"ConnectionString": "DefaultEndpointsProtocol=https;AccountName=reconnectdev;AccountKey=your-account-key;EndpointSuffix=core.windows.net", "StorageAccountName": "reconnectdev", "Containers": ["files", "reports", "logs"]}, "archive": {"ConnectionString": "DefaultEndpointsProtocol=https;AccountName=archivestore;AccountKey=your-account-key;EndpointSuffix=core.windows.net", "StorageAccountName": "archivestore", "Containers": ["archived-files", "old-documents"]}}}}