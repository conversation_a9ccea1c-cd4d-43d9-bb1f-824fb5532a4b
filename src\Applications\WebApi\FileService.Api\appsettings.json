{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "AzureStorage": {"DefaultSasExpiryHours": 1, "MaxSasExpiryHours": 24, "StorageAccounts": {"rehash3dev": {"ConnectionString": "DefaultEndpointsProtocol=https;AccountName=rehash3dev;AccountKey=your-account-key;EndpointSuffix=core.windows.net", "Containers": ["files", "documents", "images"]}, "rehash3prod": {"ConnectionString": "DefaultEndpointsProtocol=https;AccountName=rehash3prod;AccountKey=your-account-key;EndpointSuffix=core.windows.net", "Containers": ["files", "reports", "logs"]}, "rehash3archive": {"ConnectionString": "DefaultEndpointsProtocol=https;AccountName=rehash3archive;AccountKey=your-account-key;EndpointSuffix=core.windows.net", "Containers": ["archived-files", "old-documents"]}}}}