using FileService.Core.Configuration;
using FileService.Core.Interfaces;
using FileService.HttpClients.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace FileService.HttpClients.Extensions;

/// <summary>
/// Extension methods for registering Azure Blob Storage services
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Adds Azure Blob Storage services to the service collection
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configuration">The configuration</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddAzureBlobStorage(this IServiceCollection services, IConfiguration configuration)
    {
        // Configure Azure Storage options
        services.Configure<AzureStorageOptions>(configuration.GetSection(AzureStorageOptions.SectionName));

        // Register the Azure Blob Service
        services.AddScoped<IAzureBlobService, AzureBlobService>();

        return services;
    }

    /// <summary>
    /// Adds Azure Blob Storage services to the service collection with custom configuration
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configureOptions">Action to configure Azure Storage options</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddAzureBlobStorage(this IServiceCollection services, Action<AzureStorageOptions> configureOptions)
    {
        // Configure Azure Storage options
        services.Configure(configureOptions);

        // Register the Azure Blob Service
        services.AddScoped<IAzureBlobService, AzureBlobService>();

        return services;
    }
}
