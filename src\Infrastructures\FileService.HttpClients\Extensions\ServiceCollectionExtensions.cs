using FileService.Core.Configuration;
using FileService.Core.Interfaces;
using FileService.HttpClients.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace FileService.HttpClients.Extensions;

/// <summary>
/// Extension methods for registering Azure Blob SAS services
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Adds Azure Blob SAS services to the service collection
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configuration">The configuration</param>
    public static void AddAzureBlobStorage(this IServiceCollection services, IConfiguration configuration)
    {
        // Configure Azure Storage options
        services.Configure<AzureStorageOptions>(configuration.GetSection(AzureStorageOptions.SectionName));

        // Register the Azure Blob SAS Service
        services.AddScoped<IAzureBlobSasService, AzureBlobSasService>();
    }

    /// <summary>
    /// Adds Azure Blob SAS services to the service collection with custom configuration
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configureOptions">Action to configure Azure Storage options</param>
    public static void AddAzureBlobStorage(this IServiceCollection services, Action<AzureStorageOptions> configureOptions)
    {
        // Configure Azure Storage options
        services.Configure(configureOptions);

        // Register the Azure Blob SAS Service
        services.AddScoped<IAzureBlobSasService, AzureBlobSasService>();
    }
}
