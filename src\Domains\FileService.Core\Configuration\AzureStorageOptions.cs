namespace FileService.Core.Configuration;

/// <summary>
/// Configuration options for Azure Storage with support for multiple connection strings
/// </summary>
public class AzureStorageOptions
{
    /// <summary>
    /// Configuration section name
    /// </summary>
    public const string SectionName = "AzureStorage";

    /// <summary>
    /// Multiple named storage accounts with their connection strings and containers
    /// </summary>
    public Dictionary<string, StorageAccountConfig> StorageAccounts { get; set; } = new();

    /// <summary>
    /// Default SAS token expiry duration in hours
    /// </summary>
    public int DefaultSasExpiryHours { get; set; } = 1;

    /// <summary>
    /// Maximum allowed SAS token expiry duration in hours
    /// </summary>
    public int MaxSasExpiryHours { get; set; } = 24;

    /// <summary>
    /// Validates the configuration
    /// </summary>
    /// <returns>True if valid, false otherwise</returns>
    public bool IsValid()
    {
        return StorageAccounts.Count > 0 && StorageAccounts.Values.All(account => account.IsValid());
    }

    /// <summary>
    /// Gets a storage account configuration by name
    /// </summary>
    /// <param name="accountName">The storage account name</param>
    /// <returns>The storage account configuration or null if not found</returns>
    public StorageAccountConfig? GetStorageAccount(string accountName)
    {
        return StorageAccounts.TryGetValue(accountName, out var account) ? account : null;
    }

    /// <summary>
    /// Gets all available storage account names
    /// </summary>
    /// <returns>Collection of storage account names</returns>
    public IEnumerable<string> GetStorageAccountNames()
    {
        return StorageAccounts.Keys;
    }
}

/// <summary>
/// Configuration for a single storage account
/// </summary>
public class StorageAccountConfig
{
    /// <summary>
    /// The Azure Storage connection string
    /// </summary>
    public string ConnectionString { get; set; } = string.Empty;

    /// <summary>
    /// The actual Azure Storage account name (e.g., "rehash3dev")
    /// </summary>
    public string StorageAccountName { get; set; } = string.Empty;

    /// <summary>
    /// The Azure Storage account name (alternative to connection string)
    /// </summary>
    public string? AccountName { get; set; }

    /// <summary>
    /// The Azure Storage account key (alternative to connection string)
    /// </summary>
    public string? AccountKey { get; set; }

    /// <summary>
    /// The Azure Storage SAS token (alternative to connection string)
    /// </summary>
    public string? SasToken { get; set; }

    /// <summary>
    /// The Azure Storage blob service endpoint (for custom endpoints)
    /// </summary>
    public string? BlobServiceEndpoint { get; set; }

    /// <summary>
    /// List of containers available in this storage account
    /// </summary>
    public List<string> Containers { get; set; } = new();

    /// <summary>
    /// Whether to use HTTPS for blob URLs
    /// </summary>
    public bool UseHttps { get; set; } = true;

    /// <summary>
    /// Validates the storage account configuration
    /// </summary>
    /// <returns>True if valid, false otherwise</returns>
    public bool IsValid()
    {
        // Must have storage account name
        if (string.IsNullOrEmpty(StorageAccountName))
            return false;

        // Must have either connection string or account name/key combination
        if (!string.IsNullOrEmpty(ConnectionString))
            return true;

        if (!string.IsNullOrEmpty(AccountName) &&
            (!string.IsNullOrEmpty(AccountKey) || !string.IsNullOrEmpty(SasToken)))
            return true;

        return false;
    }

    /// <summary>
    /// Gets the blob service URI
    /// </summary>
    /// <returns>The blob service URI</returns>
    public Uri? GetBlobServiceUri()
    {
        if (!string.IsNullOrEmpty(BlobServiceEndpoint))
            return new Uri(BlobServiceEndpoint);

        if (!string.IsNullOrEmpty(StorageAccountName))
        {
            var scheme = UseHttps ? "https" : "http";
            return new Uri($"{scheme}://{StorageAccountName}.blob.core.windows.net");
        }

        if (!string.IsNullOrEmpty(AccountName))
        {
            var scheme = UseHttps ? "https" : "http";
            return new Uri($"{scheme}://{AccountName}.blob.core.windows.net");
        }

        return null;
    }

    /// <summary>
    /// Checks if a container is configured for this storage account
    /// </summary>
    /// <param name="containerName">The container name to check</param>
    /// <returns>True if the container is configured, false otherwise</returns>
    public bool HasContainer(string containerName)
    {
        return Containers.Contains(containerName, StringComparer.OrdinalIgnoreCase);
    }
}
