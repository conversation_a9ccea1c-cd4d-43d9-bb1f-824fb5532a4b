namespace FileService.Core.Configuration;

/// <summary>
/// Configuration options for Azure Storage
/// </summary>
public class AzureStorageOptions
{
    /// <summary>
    /// Configuration section name
    /// </summary>
    public const string SectionName = "AzureStorage";

    /// <summary>
    /// The Azure Storage connection string
    /// </summary>
    public string ConnectionString { get; set; } = string.Empty;

    /// <summary>
    /// The Azure Storage account name (alternative to connection string)
    /// </summary>
    public string? AccountName { get; set; }

    /// <summary>
    /// The Azure Storage account key (alternative to connection string)
    /// </summary>
    public string? AccountKey { get; set; }

    /// <summary>
    /// The Azure Storage SAS token (alternative to connection string)
    /// </summary>
    public string? SasToken { get; set; }

    /// <summary>
    /// The Azure Storage blob service endpoint (for custom endpoints)
    /// </summary>
    public string? BlobServiceEndpoint { get; set; }

    /// <summary>
    /// Default container name to use if not specified in requests
    /// </summary>
    public string? DefaultContainerName { get; set; }

    /// <summary>
    /// Default SAS token expiry duration in hours
    /// </summary>
    public int DefaultSasExpiryHours { get; set; } = 1;

    /// <summary>
    /// Maximum allowed SAS token expiry duration in hours
    /// </summary>
    public int MaxSasExpiryHours { get; set; } = 24;

    /// <summary>
    /// Whether to use HTTPS for blob URLs
    /// </summary>
    public bool UseHttps { get; set; } = true;

    /// <summary>
    /// Validates the configuration
    /// </summary>
    /// <returns>True if valid, false otherwise</returns>
    public bool IsValid()
    {
        // Must have either connection string or account name/key combination
        if (!string.IsNullOrEmpty(ConnectionString))
            return true;

        if (!string.IsNullOrEmpty(AccountName) && 
            (!string.IsNullOrEmpty(AccountKey) || !string.IsNullOrEmpty(SasToken)))
            return true;

        return false;
    }

    /// <summary>
    /// Gets the blob service URI
    /// </summary>
    /// <returns>The blob service URI</returns>
    public Uri? GetBlobServiceUri()
    {
        if (!string.IsNullOrEmpty(BlobServiceEndpoint))
            return new Uri(BlobServiceEndpoint);

        if (!string.IsNullOrEmpty(AccountName))
        {
            var scheme = UseHttps ? "https" : "http";
            return new Uri($"{scheme}://{AccountName}.blob.core.windows.net");
        }

        return null;
    }
}
